import requests
import progressbar as pb
import os

def download_file(url, file_name, dest_dir):

    if not os.path.exists(dest_dir):
        os.makedirs(dest_dir)

    full_path_to_file = dest_dir + os.path.sep + file_name

    if os.path.exists(dest_dir + os.path.sep + file_name):
        return full_path_to_file

    print("Downloading " + file_name + " from " + url)

    try:
        r = requests.get(url, allow_redirects=True, stream=True)
    except:
        print("Could not establish connection. Download failed")
        return None

    # Handle case where Content-Length header is not available
    if 'Content-Length' in r.headers:
        file_size = int(r.headers['Content-Length'])
        chunk_size = 1024
        num_bars = round(file_size / chunk_size)
        bar = pb.ProgressBar(maxval=num_bars + 1).start()  # Increment maxval by 1
    else:
        file_size = None
        chunk_size = 1024
        bar = None
        print("File size unknown, downloading without progress bar...")

    if r.status_code != requests.codes.ok:
        print("Error occurred while downloading file")
        return None

    count = 0

    with open(full_path_to_file, 'wb') as file:
        for chunk in r.iter_content(chunk_size=chunk_size):
            file.write(chunk)
            count += 1
            if bar is not None:
                if file_size and count <= num_bars:
                    bar.update(count)  # Update progress bar inside the loop
                elif file_size:
                    bar.update(num_bars)  # Ensure progress bar doesn't go out of range

    if bar is not None:
        bar.finish()  # Finish the progress bar

    return full_path_to_file
